import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

class CurvedAppBar extends StatelessWidget implements PreferredSizeWidget {
  final double height;
  final Color appBarColor;
  final Color logoColor;
  final Widget topRightIcon;
  final Widget topLeftIcon;

  CurvedAppBar({
    this.height = 250.0,
    this.appBarColor = Colors.white,
    this.logoColor = Colors.white,
    this.topRightIcon = const SizedBox(
      width: 50,
      height: 50,
    ),
    this.topLeftIcon = const SizedBox(
      width: 50,
      height: 50,
    ),
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: this.height,
      child: CustomPaint(
        painter: _CurvedShadowPainter(),
        child: ClipPath(
          clipper: _CurvedAppBarClipper(),
          child: Container(
            color: appBarColor,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Padding(
                  padding: const EdgeInsets.only(top: 0.0, left: 20),
                  child: SizedBox(height: 50, width: 80, child: topLeftIcon),
                ),
                Center(
                  child: Padding(
                    padding: const EdgeInsets.only(top: 35.0),
                    child: Align(
                      alignment: Alignment.center,
                      child: Hero(
                        tag: 'app_logo',
                        child: SvgPicture.asset(
                          'assets/logo/juno_logo_violet.svg',
                          height: 45,
                          width: 90,
                          colorFilter:
                              ColorFilter.mode(logoColor, BlendMode.srcIn),
                          fit: BoxFit.fitWidth,
                        ),
                      ),
                    ),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.only(top: 0.0, right: 20),
                  child: SizedBox(height: 50, width: 80, child: topRightIcon),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  @override
  Size get preferredSize => Size.fromHeight(height);
}

class _CurvedAppBarClipper extends CustomClipper<Path> {
  @override
  Path getClip(Size size) {
    var path = Path();
    path.lineTo(0, 0);
    path.lineTo(0, size.height - 40);
    path.quadraticBezierTo(
        size.width / 4, size.height, size.width / 2, size.height);
    path.quadraticBezierTo(
        size.width - size.width / 4, size.height, size.width, size.height - 40);
    path.lineTo(size.width, 0);
    path.lineTo(0, 0);
    return path;
  }

  @override
  bool shouldReclip(CustomClipper<Path> oldClipper) => false;
}

class _CurvedShadowPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    Path path = _CurvedAppBarClipper().getClip(size);
    canvas.drawShadow(path, Colors.black, 4.0, false);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}

class MyShapeBorder extends ContinuousRectangleBorder {
  const MyShapeBorder(this.curveHeight);

  final double curveHeight;

  @override
  Path getOuterPath(Rect rect, {TextDirection? textDirection}) {
    return Path()
      ..lineTo(
          0,
          rect.height -
              curveHeight) // Start below the top to avoid sharp corners
      ..quadraticBezierTo(
        rect.width / 2,
        rect.height + curveHeight, // Control point for the curve
        rect.width,
        rect.height - curveHeight, // End below the top right corner
      )
      ..lineTo(rect.width, 0) // Top right corner
      ..close(); // Connect back to the starting point
  }
}
