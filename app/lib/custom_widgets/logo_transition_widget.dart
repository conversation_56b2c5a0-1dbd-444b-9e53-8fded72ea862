import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// A widget that handles the smooth transition of the logo from splash to dashboard
class LogoTransitionWidget extends StatefulWidget {
  final bool isInSplash;
  final Color? logoColor;
  final VoidCallback? onTransitionComplete;

  const LogoTransitionWidget({
    Key? key,
    required this.isInSplash,
    this.logoColor,
    this.onTransitionComplete,
  }) : super(key: key);

  @override
  State<LogoTransitionWidget> createState() => _LogoTransitionWidgetState();
}

class _LogoTransitionWidgetState extends State<LogoTransitionWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<Offset> _positionAnimation;
  late Animation<double> _opacityAnimation;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    
    if (!widget.isInSplash) {
      // Start the transition animation when not in splash
      _startTransition();
    }
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    // Scale animation - logo scales down slightly during transition
    _scaleAnimation = Tween<double>(
      begin: 1.2,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    // Position animation - logo moves from center to app bar position
    _positionAnimation = Tween<Offset>(
      begin: const Offset(0, 0), // Center of screen
      end: const Offset(0, -0.6), // App bar position (adjust as needed)
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    // Opacity animation for smooth fade
    _opacityAnimation = Tween<double>(
      begin: 1.0,
      end: 1.0,
    ).animate(_animationController);

    _animationController.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        widget.onTransitionComplete?.call();
      }
    });
  }

  void _startTransition() {
    Future.delayed(const Duration(milliseconds: 100), () {
      if (mounted) {
        _animationController.forward();
      }
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (widget.isInSplash) {
      // In splash screen - show static logo that will be picked up by Hero
      return Hero(
        tag: 'app_logo_transition',
        child: SvgPicture.asset(
          'assets/logo/juno_logo_violet.svg',
          height: 45,
          width: 90,
          colorFilter: widget.logoColor != null 
              ? ColorFilter.mode(widget.logoColor!, BlendMode.srcIn)
              : null,
          fit: BoxFit.fitWidth,
        ),
      );
    } else {
      // In dashboard - show animated logo
      return AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: Transform.translate(
              offset: Offset(
                _positionAnimation.value.dx * MediaQuery.of(context).size.width,
                _positionAnimation.value.dy * MediaQuery.of(context).size.height,
              ),
              child: Opacity(
                opacity: _opacityAnimation.value,
                child: Hero(
                  tag: 'app_logo_transition',
                  child: SvgPicture.asset(
                    'assets/logo/juno_logo_violet.svg',
                    height: 45,
                    width: 90,
                    colorFilter: widget.logoColor != null 
                        ? ColorFilter.mode(widget.logoColor!, BlendMode.srcIn)
                        : null,
                    fit: BoxFit.fitWidth,
                  ),
                ),
              ),
            ),
          );
        },
      );
    }
  }
}
