import 'dart:async';
import 'package:account_management/application/menstrual_cycle_bloc/menstrual_cycle_bloc.dart';
import 'package:account_management/application/onboardin_form_bloc/onboarding_form_bloc.dart';
import 'package:account_management/account_management.dart';
import 'package:account_management/domain/facade/period_tracking_facade.dart';
import 'package:analytics/analytics.dart' hide getIt;
import 'package:authentication/application/bloc/email_verification/email_verification_bloc.dart';
import 'package:auto_route/auto_route.dart';
import 'package:bluetooth/application/bluetooth_service_bloc/bluetooth_service_bloc.dart';
import 'package:design_system/design_system.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:juno_plus/routing/app_pages.dart';
import 'package:juno_plus/routing/app_pages.gr.dart';
import 'package:authentication/authentication.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:lottie/lottie.dart';
import 'package:notifications/application/manage_scheduled_notifications_bloc/manage_scheduled_notifications_bloc.dart';
import 'services/app_lifecycle_observer.dart';
import 'helpers.dart';
import 'custom_widgets/logo_transition_widget.dart';

class JunoPlus extends StatefulWidget {
  final String env;

  JunoPlus({
    required this.env,
    Key? key,
  }) : super(key: key);

  @override
  State<JunoPlus> createState() => _JunoPlusState();
}

class _JunoPlusState extends State<JunoPlus> {
  final _appRouter = AppRouter();
  late final GlobalKey<NavigatorState> _navigatorKey;
  AppLifecycleObserver? _lifecycleObserver;

  @override
  void initState() {
    super.initState();
    // Get the navigator key from dependency injection to ensure consistency
    _navigatorKey = getIt<GlobalKey<NavigatorState>>();
    _initializeLifecycleObserver();
    // Check for pending feedback on app startup with a slight delay
    WidgetsBinding.instance.addPostFrameCallback((_) {
      print('🔍 App startup - checking for pending feedback');
      // Add a small delay to ensure the navigator context is available
      Future.delayed(const Duration(milliseconds: 500), () {});
    });
  }

  void _initializeLifecycleObserver() {
    _lifecycleObserver = AppLifecycleObserver(_handleAppLifecycleChange);
    _lifecycleObserver!.initialize();
  }

  void _handleAppLifecycleChange(AppLifecycle lifecycle) {
    if (lifecycle == AppLifecycle.resumed) {
      print('📱 App resumed - checking for pending feedback with delay');
      // App came to foreground - check for pending feedback with delay to ensure UI is ready
      Future.delayed(const Duration(milliseconds: 1000), () {});
    }
  }

  @override
  void dispose() {
    _lifecycleObserver?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ScreenUtilInit(
      designSize: const Size(750, 1334),
      minTextAdapt: true,
      splitScreenMode: true,
      builder: (context, child) {
        return MultiBlocProvider(
          providers: [
            BlocProvider(
              create: (_) =>
                  getIt<AuthBloc>()..add(const AuthEvent.authCheckRequested()),
            ),
            BlocProvider(
                create: (_) => getIt<ManageScheduledNotificationsBloc>()),
            BlocProvider(
              create: (_) => getIt<BluetoothServiceBloc>()
                ..add(const BluetoothServiceEvent.checkBluetooth()),
            ),
            BlocProvider<OnboardingFormBloc>(
              create: (context) => getIt<OnboardingFormBloc>(),
            ),
            BlocProvider<SignInBloc>(
              create: (context) => getIt<SignInBloc>(),
            ),
            BlocProvider<EmailVerificationBloc>(
              create: (context) => getIt<EmailVerificationBloc>(),
            ),
            BlocProvider<MenstrualCycleBloc>(
              create: (context) => getIt<MenstrualCycleBloc>()
                // ..add(MenstrualCycleEvent.getInitialData())
                ..add(MenstrualCycleEvent.watchAllStarted()),
            ),
            // Add AnalyticsInitializationBloc to handle analytics startup
            BlocProvider<AnalyticsInitializationBloc>(
              create: (context) => getIt<AnalyticsInitializationBloc>()
                ..add(InitializeAnalyticsEvent()),
            ),
            // Initialize period reminders on app startup
            BlocProvider<PeriodReminderSettingsBloc>(
              create: (context) {
                final bloc = getIt<PeriodReminderSettingsBloc>();
                // Initialize period reminders system
                getIt<PeriodTrackingFacade>().initializePeriodReminders();
                return bloc
                  ..add(const PeriodReminderSettingsEvent.loadSettings());
              },
            ),
            // Add TherapyAnalyticsBloc for chart functionality
            BlocProvider<TherapyAnalyticsBloc>(
              create: (context) => getIt<TherapyAnalyticsBloc>(),
            ),
          ],
          child: MaterialApp.router(
            routerConfig: _appRouter.config(),
            key: _navigatorKey, // Use our navigator key
            title: 'Juno Plus',
            theme: AppTheme.lightTheme,
            debugShowCheckedModeBanner: false,
          ),
        );
      },
    );
  }
}

@RoutePage()
class SplashPage extends StatefulWidget {
  const SplashPage({super.key});

  @override
  State<SplashPage> createState() => _SplashPageState();
}

class _SplashPageState extends State<SplashPage> with TickerProviderStateMixin {
  bool isUserAuthenticated = false;
  bool hasNavigated = false;
  bool showLogo = false;
  StreamSubscription<AuthState>? _authSubscription;
  late AnimationController _logoController;
  late Animation<double> _logoOpacity;

  @override
  void initState() {
    super.initState();
    _setupLogoAnimation();
    navigateUser();
  }

  void _setupLogoAnimation() {
    _logoController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );

    _logoOpacity = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _logoController,
      curve: Curves.easeIn,
    ));
  }

  Future<void> navigateUser() async {
    // Wait for the Lottie animation to finish (1.8 seconds to show logo before transition)
    await Future.delayed(const Duration(milliseconds: 1800));

    // Show the logo for smooth transition
    if (mounted) {
      setState(() {
        showLogo = true;
      });
      _logoController.forward();
    }

    // Wait a bit more for the logo to appear, then navigate
    await Future.delayed(const Duration(milliseconds: 400));

    if (!mounted) return;

    final authBloc = BlocProvider.of<AuthBloc>(context);
    _authSubscription = authBloc.stream.listen((state) {
      if (hasNavigated) return;

      state.maybeMap(
        authenticated: (state) {
          // Navigate to HomePage
          if (state.user.isEmailVerified!) {
            if (state.user.isOnboarded!) {
              context.router.popAndPush(HomeRoute());
            } else {
              context.router.popAndPush(GetStartedRoute());
            }
          } else {
            context.router.popAndPush(EmailVerificationRoute());
          }
          hasNavigated = true;
        },
        unauthenticated: (_) {
          // Navigate to WelcomePage
          context.router.popAndPush(ProductShowcaseRoute());
          hasNavigated = true;
        },
        orElse: () {},
      );
    });
    authBloc.add(const AuthEvent.authCheckRequested());
  }

  @override
  void dispose() {
    _authSubscription?.cancel();
    _logoController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Center(
        child: Stack(
          alignment: Alignment.center,
          children: [
            // Lottie animation for splash screen
            Lottie.asset(
              'assets/rive/logo_lottie.json',
              width: 1.sw,
              height: 400,
              fit: BoxFit.fitWidth,
            ),
            // Show logo for smooth transition after Lottie animation
            if (showLogo)
              AnimatedBuilder(
                animation: _logoOpacity,
                builder: (context, child) {
                  return Opacity(
                    opacity: _logoOpacity.value,
                    child: Hero(
                      tag: 'app_logo',
                      child: LogoTransitionWidget(
                        isInSplash: true,
                        logoColor: const Color(0xff30285D),
                      ),
                    ),
                  );
                },
              ),
          ],
        ),
      ),
    );
  }
}
